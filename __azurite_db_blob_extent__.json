{"filename": "c:\\Users\\<USER>\\RiderProjects\\EservicesRESTapis\\__azurite_db_blob_extent__.json", "collections": [{"name": "$EXTENTS_COLLECTION$", "data": [{"id": "e3bf8800-e665-4945-9fa6-4bd1e7b72d20", "locationId": "<PERSON><PERSON><PERSON>", "path": "e3bf8800-e665-4945-9fa6-4bd1e7b72d20", "size": 0, "lastModifiedInMS": 1753091266809, "meta": {"revision": 0, "created": 1753091266810, "version": 0}, "$loki": 1}], "idIndex": null, "binaryIndices": {"id": {"name": "id", "dirty": false, "values": [0]}}, "constraints": null, "uniqueNames": [], "transforms": {}, "objType": "$EXTENTS_COLLECTION$", "dirty": false, "cachedIndex": null, "cachedBinaryIndex": null, "cachedData": null, "adaptiveBinaryIndices": true, "transactional": false, "cloneObjects": false, "cloneMethod": "parse-stringify", "asyncListeners": false, "disableMeta": false, "disableChangesApi": true, "disableDeltaChangesApi": true, "autoupdate": false, "serializableIndices": true, "disableFreeze": true, "ttl": null, "maxId": 1, "DynamicViews": [], "events": {"insert": [], "update": [], "pre-insert": [], "pre-update": [], "close": [], "flushbuffer": [], "error": [], "delete": [null], "warning": [null]}, "changes": [], "dirtyIds": []}], "databaseVersion": 1.5, "engineVersion": 1.5, "autosave": true, "autosaveInterval": 5000, "autosaveHandle": null, "throttledSaves": true, "options": {"persistenceMethod": "fs", "autosave": true, "autosaveInterval": 5000, "serializationMethod": "normal", "destructureDelimiter": "$<\n"}, "persistenceMethod": "fs", "persistenceAdapter": null, "verbose": false, "events": {"init": [null], "loaded": [], "flushChanges": [], "close": [], "changes": [], "warning": []}, "ENV": "NODEJS"}