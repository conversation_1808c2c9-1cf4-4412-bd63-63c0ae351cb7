using EServiceFunctions.Models.Transfer;
using EServiceFunctions.RequestResponseModels.Transfer;
using static EServiceFunctions.MapperModels.TransferMapper;

namespace EServiceFunctions.Functions;

public class TransferFunctions(
    IDbContextFactory<TransferContext> transferDbContextFactory,
    ILogger<TransferFunctions> logger)
{
    private const string UnauthorizedQIdAccessTemplate = "Unauthorized ({QId}) Access!!!";
    private const string ErrorMessageTemplate = "Error: {Message}";

    #region GetTransferListByQID

    /// <summary>
    /// Get the transfer list for the given submitter's QId
    /// </summary>
    /// <param name="req"></param>
    /// <param name="qId">Submitter's QId</param>
    /// <returns></returns>
    [Function("GetTransferListByQID")]
    [OpenApiOperation(operationId: "GetTransferListByQID", tags: ["Transfer"],
        Summary = "Get Transfer List By Submitter's QID",
        Description = " Get the transfer list for the given submitter's QId")]
    [OpenApiParameter("qId", Description = "Submitter's QId", In = ParameterLocation.Path, Required = true,
        Type = typeof(long))]
    [OpenApiParameter("status",
        Description =
            "Please provide a valid status: InProcess {Submitted, Rework, Reworked, ConditionallyApproved, ResubmitOriginalsRequested}  or Archived {Approved, Cancelled, CancelledByEServ}.",
        In = ParameterLocation.Query, Required = false, Type = typeof(string))]
    [OpenApiParameter("skip", Description = "Skip Records", In = ParameterLocation.Query, Required = false,
        Type = typeof(int))]
    [OpenApiParameter("take", Description = "Take Records", In = ParameterLocation.Query, Required = false,
        Type = typeof(int))]
    [OpenApiResponseWithBody(OK, "application/json", typeof(List<GetTransferListResponse>),
        Description =
            "Type of requests to return viz. {Submitted, Rework, Reworked, ConditionallyApproved, ResubmitOriginalsRequested, Approved, Cancelled, CancelledByEServ}.")]
    [OpenApiResponseWithBody(BadRequest, "application/json", typeof(object))]
    [OpenApiResponseWithBody(NoContent, "application/json", typeof(object))]
    public async Task<HttpResponseData> GetTransferListByQId(
        [HttpTrigger(AuthorizationLevel.Function, "get", Route = "transfers/submitter-qid/{qId}")]
        HttpRequestData req, long qId)
    {
        try
        {
            logger.LogInformation("Getting Transfer List By Submitter's QID {QId}", qId);
            
            if (!req.IsAuthorized(qId))
            {
                logger.LogWarning(UnauthorizedQIdAccessTemplate, qId);
                return await req.WriteUnauthorizedResponseAsync();
            }

            string transferStatus = req.GetCleanedQueryString("status");
            int skip = req.GetIntQueryParameter("skip");
            int take = req.GetIntQueryParameter("take");

            await using var dbContext = await transferDbContextFactory.CreateDbContextAsync();

            var query = dbContext.Transfer!.Join(dbContext.Status!, e => e.Status, s => s.Code,
                (transfer, status) => new { transfer, status }).AsQueryable().AsNoTracking();

            query = query.Where(item => item.transfer.SubmitterQId == qId);

            if (!string.IsNullOrWhiteSpace(transferStatus))
            {
                query = query.Where(item =>
                    item.status.Category != null &&
                    (item.status.Category == transferStatus || item.transfer.Status == transferStatus));
            }

            var response = await query.Select(item => new GetTransferListResponse
            {
                QId = item.transfer.QId,
                ReqNumber = item.transfer.ReqNumber,
                FNameEn = item.transfer.FNameEn,
                MNameEn = item.transfer.MNameEn,
                LNameEn = item.transfer.LNameEn,
                FNameAr = item.transfer.FNameAr,
                MNameAr = item.transfer.MNameAr,
                LNameAr = item.transfer.LNameAr,
                Status = item.transfer.StatusNavigation!.Code,
                StatusDescriptionEn = item.transfer.StatusNavigation.DescriptionEn,
                StatusDescriptionAr = item.transfer.StatusNavigation.DescriptionAr,
                SubmittedAt = item.transfer.SubmittedAt.ToUtcString()
            }).OrderBy(p => p.Status).Skip(skip).Take(take).ToListAsync();

            if (response.Count == 0)
            {
                logger.LogInformation("Items not found for the given QID {QId} and Status {Status}", qId, transferStatus);
                return await req.WriteNoContentResponseAsync();
            }

            logger.LogInformation("Returning Transfer List ({Count}) By Submitter's QID {QId}", response.Count, qId);
            
            return await req.WriteOkResponseAsync(response);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, ErrorMessageTemplate, ex.Message);
            return ex.DefaultExceptionBehaviour(req, logger);
        }
    }

    #endregion

    #region GetTransferStatsByQID

    /// <summary>
    /// Get the transfer stats for the given submitter's QId
    /// </summary>
    /// <param name="req"></param>
    /// <param name="qId">Submitter's QId</param>
    /// <returns></returns>
    [Function("GetTransferStatsByQID")]
    [OpenApiOperation(operationId: "GetTransferStatsByQID", tags: ["Transfer"],
        Summary = "Get Transfer Stats By Submitter's QID",
        Description = "Get the transfer stats for the given submitter's QId")]
    [OpenApiParameter("qId", Description = "Submitter's QId", In = ParameterLocation.Path, Required = true,
        Type = typeof(long))]
    [OpenApiParameter("status",
        Description =
            "Please provide a valid status: InProcess {Submitted, Rework, Reworked, ConditionallyApproved, ResubmitOriginalsRequested}  or Archived {Approved, Cancelled, CancelledByEServ}.",
        In = ParameterLocation.Query, Required = false, Type = typeof(string))]
    [OpenApiResponseWithBody(OK, "application/json", typeof(GetTransferStatsResponse))]
    [OpenApiResponseWithBody(BadRequest, "application/json", typeof(object))]
    public async Task<HttpResponseData> GetTransferStatsByQId(
        [HttpTrigger(AuthorizationLevel.Function, "get", Route = "transfers/submitter-qid/{qId}/stats")]
        HttpRequestData req, long qId)
    {
        try
        {
            logger.LogInformation("Getting Transfer Stats By Submitter's QID {QId}", qId);
            
            if (!req.IsAuthorized(qId))
            {
                logger.LogWarning(UnauthorizedQIdAccessTemplate, qId);
                return await req.WriteUnauthorizedResponseAsync();
            }
            
            string transferStatus = req.GetCleanedQueryString("status");

            await using var dbContext = await transferDbContextFactory.CreateDbContextAsync();

            var query = dbContext.Transfer!.Join(dbContext.Status!, e => e.Status, s => s.Code,
                (transfer, status) => new { transfer, status }).AsQueryable().AsNoTracking();

            query = query.Where(item => item.transfer.SubmitterQId == qId);
            
            int queryCount = await query.CountAsync();
            
            if (queryCount == 0)
            {
                logger.LogInformation("Items not found for the given QID {QId} and Status {Status}", qId, transferStatus);
                return await req.WriteNoContentResponseAsync();
            }

            if (!string.IsNullOrWhiteSpace(transferStatus))
            {
                query = query.Where(item =>
                    item.status.Category! == transferStatus || item.transfer.Status! == transferStatus);
            }

            var response = await query.CountAsync();

            logger.LogInformation("Returning Transfers count {Count} By Submitter's QID {QId} & Status {Status}", response, qId, transferStatus);
            
            return await req.WriteOkResponseAsync(new GetTransferStatsResponse { Count = response });
        }
        catch (Exception ex)
        {
            logger.LogError(ex, ErrorMessageTemplate, ex.Message);
            return ex.DefaultExceptionBehaviour(req, logger);
        }
    }

    #endregion

    #region CheckInProcessTransferByQID

    /// <param name="req"></param>
    /// <param name="qId"></param>
    /// <returns></returns>
    [Function("CheckInProcessTransferByQID")]
    [OpenApiOperation(operationId: "CheckInProcessTransferByQID", tags: ["Transfer"],
        Summary = "Check InProcess Transfer(s) By Requester's QID",
        Description = "Check inprocess Transfer(s) for the given Requester's QId")]
    [OpenApiParameter("qId", Description = "Requester's QId", In = ParameterLocation.Path, Required = true,
        Type = typeof(long))]
    [OpenApiResponseWithBody(OK, "application/json", typeof(CheckInProcessTransferResponse),
        Description = "Returns True Or False")]
    [OpenApiResponseWithBody(BadRequest, "application/json", typeof(object))]
    public async Task<HttpResponseData> CheckInProcessTransferByQId(
        [HttpTrigger(AuthorizationLevel.Function, "get", Route = "transfers/{qId}/inprocess-validation")]
        HttpRequestData req, long qId)
    {
        try
        {
            var submitter = req.GetClaims();
            logger.LogInformation("Checking InProcess Transfer(s) By Requester's QID {QId}", submitter.QId);

            await using var dbContext = await transferDbContextFactory.CreateDbContextAsync();

            var query = dbContext.Transfer!.Join(dbContext.Status!, e => e.Status, s => s.Code,
                (transfer, status) => new { transfer, status }).AsQueryable().AsNoTracking();
            var isInProcessRequestExist = await query.AnyAsync(item =>
                item.transfer.QId == qId && item.status.Category! == InProcess);

            logger.LogInformation("Returning InProcess Transfer(s) Validation Result {Result} By Requester's QID {QId}", isInProcessRequestExist, qId);

            return await req.WriteOkResponseAsync(new
                CheckInProcessTransferResponse
                {
                    IsInprocessExist = isInProcessRequestExist
                });
        }
        catch (Exception ex)
        {
            logger.LogError(ex, ErrorMessageTemplate, ex.Message);
            return ex.DefaultExceptionBehaviour(req, logger);
        }
    }

    #endregion

    #region GetTransferItemByReqNumber

    /// <summary>
    /// Get transfer item for the given request number
    /// </summary>
    /// <param name="req"></param>
    /// <param name="reqNumber">Transfer Request Number</param>
    /// <returns></returns>
    [Function("GetTransferItemByReqNumber")]
    [OpenApiOperation(operationId: "GetTransferItemByReqNumber", tags: ["Transfer"],
        Summary = "Get Transfer Item By ReqNumber", Description = "Get transfer item for the given request number")]
    [OpenApiParameter("reqNumber", Description = "Transfer Request Number", In = ParameterLocation.Path,
        Required = true, Type = typeof(string))]
    [OpenApiResponseWithBody(OK, "application/json", typeof(GetTransferItemResponse))]
    [OpenApiResponseWithBody(BadRequest, "application/json", typeof(object))]
    [OpenApiResponseWithBody(NoContent, "application/json", typeof(object))]
    public async Task<HttpResponseData> GetTransferItemByReqNumber(
        [HttpTrigger(AuthorizationLevel.Function, "get", Route = "transfers/{reqNumber}")]
        HttpRequestData req, string reqNumber)
    {
        try
        {
            var submitter = req.GetClaims();

            if (string.IsNullOrWhiteSpace(reqNumber))
            {
                return await req.WriteErrorResponseAsync(BadRequest, $"Invalid Req Number {reqNumber}");
            }

            logger.LogInformation("Getting Transfer Item By Request Number {ReqNumber}", reqNumber);

            await using var dbContext = await transferDbContextFactory.CreateDbContextAsync();

            var response = await dbContext.Transfer!
                .AsNoTracking()
                .Include(c1 => c1.StatusNavigation)
                .Include(c2 => c2.TransferReasonNavigation)
                .SingleOrDefaultAsync(item => item.ReqNumber! == reqNumber);

            if (response == null)
            {
                logger.LogInformation("Item not found");
                return await req.WriteNoContentResponseAsync();
            }

            if (submitter.QId != null && response.SubmitterQId != submitter.QId)
            {
                logger.LogWarning(UnauthorizedQIdAccessTemplate, response.SubmitterQId);
                return await req.WriteUnauthorizedResponseAsync();
            }

            var getResponse = MapTransferToGetTransferItemResponse(response);
            
            getResponse.TransferReason = response.TransferReasonNavigation?.ReasonCode;
            getResponse.TransferReasonDescriptionEn = response.TransferReasonNavigation?.ReasonEn;
            getResponse.TransferReasonDescriptionAr = response.TransferReasonNavigation?.ReasonAr;
            getResponse.Status = response.StatusNavigation?.Code;
            getResponse.StatusDescriptionEn = response.StatusNavigation?.DescriptionEn;
            getResponse.StatusDescriptionAr = response.StatusNavigation?.DescriptionAr;
            logger.LogInformation("Returning Transfer Item By Request Number {ReqNumber}", getResponse.ReqNumber);

            return await req.WriteOkResponseAsync(getResponse);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, ErrorMessageTemplate, ex.Message);
            return ex.DefaultExceptionBehaviour(req, logger);
        }
    }

    #endregion

    #region CreateTransfer

    /// <summary>
    /// Create new transfer request
    /// </summary>
    /// <param name="req">New Transfer Create Model</param>
    /// <returns></returns>
    [Function("CreateTransfer")]
    [OpenApiOperation(operationId: "CreateTransfer", tags: ["Transfer"], Summary = "Create Transfer",
        Description = "Create new transfer request")]
    [OpenApiRequestBody("application/json", typeof(CreateUpdateTransferRequest))]
    [OpenApiResponseWithBody(Created, "application/json", typeof(Transfer))]
    [OpenApiResponseWithBody(BadRequest, "application/json", typeof(object))]
    [OpenApiResponseWithBody(InternalServerError, "application/json", typeof(object))]
    public async Task<HttpResponseData> CreateTransfer(
        [HttpTrigger(AuthorizationLevel.Function, "post", Route = "transfers")]
        HttpRequestData req)
    {
        try
        {
            logger.LogInformation("Creating An Transfer");
            var requestOriginSource = req.TryGetHeader(RequestOrigin);

            if (string.IsNullOrWhiteSpace(requestOriginSource))
            {
                return await req.WriteErrorResponseAsync(BadRequest, "Required 'X-RequestOrigin' header value");
            }

            string requestBody = await new StreamReader(req.Body).ReadToEndAsync();
            var request = DeserializeObject<CreateUpdateTransferRequest>(requestBody);

            if (request == null || IsEmptyObject(request))
            {
                return await req.WriteErrorResponseAsync(BadRequest, "Invalid request body. Please try again.");
            }
            
            long qId = request.SubmitterQId ?? 0;

            if (!req.IsAuthorized(qId))
            {
                logger.LogWarning(UnauthorizedQIdAccessTemplate, qId);
                return await req.WriteUnauthorizedResponseAsync();
            }

            var healthCardValidation = await ValidateHealthCardNumberAsync(
                request.HCNumber, request.QId, "Transfer", req, logger);

            if (!healthCardValidation.IsValid)
            {
                return healthCardValidation.ErrorResponse!;
            }

            Transfer create = MapCreateUpdateTransferRequestToTransfer(request);
            create.ReqNumber = RandomPassword();
            create.SubmittedAt = GetCurrentTime();
            create.CreatedAt = GetCurrentTime();
            create.CreateSource = requestOriginSource;
            create.Status = Saved;

            await using var dbContext = await transferDbContextFactory.CreateDbContextAsync();

            // Check for existing in-process transfer requests
            var existingInProcessQuery = dbContext.Transfer!
                .Join(dbContext.Status!, t => t.Status, s => s.Code, (transfer, status) => new { transfer, status })
                .AsQueryable();

            var isInProcessRequestExist = await existingInProcessQuery.AnyAsync(item =>
                item.transfer.QId == request.QId && item.status.Category! == InProcess);

            if (isInProcessRequestExist)
            {
                logger.LogWarning("Duplicate transfer request detected for QId {QId}", request.QId);
                return await req.WriteErrorResponseAsync(BadRequest, "An in-process transfer request already exists for this applicant.");
            }

            try
            {
                await dbContext.Transfer!.AddAsync(create);
                await dbContext.SaveChangesAsync();
            }
            catch (DbUpdateException ex)
            {
                logger.LogError(ex, ErrorMessageTemplate, ex.Message);
                return ex.DefaultExceptionBehaviour(req, logger);
            }

            logger.LogInformation("Created A New Transfer With Request Number {ReqNumber}", create.ReqNumber);
            
            return await req.WriteOkResponseAsync(create, Created);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, ErrorMessageTemplate, ex.Message);
            return ex.DefaultExceptionBehaviour(req, logger);
        }
    }

    #endregion

    #region UpdateTransferByReqNumber

    /// <summary>
    /// Update transfer for the given request number
    /// </summary>
    /// <param name="req"></param>
    /// <param name="reqNumber">Transfer Request Number </param>
    /// <returns></returns>
    [Function("UpdateTransferByReqNumber")]
    [OpenApiOperation(operationId: "UpdateTransferByReqNumber", tags: ["Transfer"],
        Summary = "Update Transfer By ReqNumber", Description = "Update transfer item for the given request number")]
    [OpenApiRequestBody("application/json", typeof(CreateUpdateTransferRequest))]
    [OpenApiParameter("reqNumber", Description = "Transfer Request Number", In = ParameterLocation.Path,
        Required = true, Type = typeof(string))]
    [OpenApiResponseWithBody(BadRequest, "application/json", typeof(object))]
    [OpenApiResponseWithBody(NoContent, "application/json", typeof(object))]
    [OpenApiResponseWithBody(InternalServerError, "application/json", typeof(object))]
    public async Task<HttpResponseData> UpdateTransferByReqNumber(
        [HttpTrigger(AuthorizationLevel.Function, "put", Route = "transfers/{reqNumber}")]
        HttpRequestData req, string reqNumber)
    {
        try
        {
            logger.LogInformation("Updating a Transfer Item By Request Number");

            var requestOriginSource = req.TryGetHeader(RequestOrigin);

            if (string.IsNullOrWhiteSpace(requestOriginSource))
            {
                return await req.WriteErrorResponseAsync(BadRequest, "Required 'X-RequestOrigin' header value");
            }

            string requestBody = await new StreamReader(req.Body).ReadToEndAsync();
            var request = DeserializeObject<CreateUpdateTransferRequest>(requestBody);

            if (request == null || IsEmptyObject(request))
            {
                return await req.WriteErrorResponseAsync(BadRequest, "Invalid request body. Please try again.");
            }
            
            long qId = request.SubmitterQId ?? 0;

            if (!req.IsAuthorized(qId))
            {
                logger.LogWarning(UnauthorizedQIdAccessTemplate, qId);
                return await req.WriteUnauthorizedResponseAsync();
            }
            
            var healthCardValidation = await ValidateHealthCardNumberAsync(
                request.HCNumber, request.QId, "Transfer", req, logger);

            if (!healthCardValidation.IsValid)
            {
                return healthCardValidation.ErrorResponse!;
            }

            await using var dbContext = await transferDbContextFactory.CreateDbContextAsync();
            Transfer? response = await dbContext.Transfer!.SingleOrDefaultAsync(item => item.ReqNumber! == reqNumber);

            if (response is null || IsEmptyObject(response))
            {
                logger.LogWarning("Invalid Request Number {ReqNumber}", reqNumber);
                return await req.WriteErrorResponseAsync(BadRequest, $"Invalid Request Number {reqNumber}");
            }

            var mapperResponse = MapCreateUpdateTransferRequestToTransfer(request);
            mapperResponse.Id = response.Id;
            mapperResponse.ReqNumber = response.ReqNumber;
            mapperResponse.Status = response.Status;
            mapperResponse.StatusInternal = response.StatusInternal;
            mapperResponse.SN = response.SN;
            mapperResponse.SubmittedAt = GetCurrentTime();
            mapperResponse.CreatedAt = response.CreatedAt;
            mapperResponse.UpdatedAt = GetCurrentTime();
            mapperResponse.CreateSource = response.CreateSource;
            mapperResponse.UpdateSource = requestOriginSource;
            
            try
            {
                dbContext.Entry(response).CurrentValues.SetValues(mapperResponse);
                await dbContext.SaveChangesAsync();
            }
            catch (DbUpdateException ex)
            {
                logger.LogError(ex, ErrorMessageTemplate, ex.Message);
                return ex.DefaultExceptionBehaviour(req, logger);
            }
            
            logger.LogInformation("Updated A New Transfer With Request Number {ReqNumber}", response.ReqNumber);

            return await req.WriteOkResponseAsync(mapperResponse);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, ErrorMessageTemplate, ex.Message);
            return ex.DefaultExceptionBehaviour(req, logger);
        }
    }

    #endregion

    #region DeleteTransferByReqNumber

    /// <summary>
    /// Delete transfer for the given request number
    /// </summary>
    /// <param name="req"></param>
    /// <param name="reqNumber">Transfer Request Number </param>
    /// <returns></returns>
    [Function("DeleteTransferByReqNumber")]
    [OpenApiOperation(operationId: "DeleteTransferByReqNumber", tags: ["Transfer"],
        Summary = "Delete Transfer By ReqNumber", Description = "Delete transfer item for the given request number")]
    [OpenApiParameter("reqNumber", Description = "Transfer Request Number", In = ParameterLocation.Path,
        Required = true, Type = typeof(string))]
    [OpenApiResponseWithBody(BadRequest, "application/json", typeof(object))]
    [OpenApiResponseWithBody(NoContent, "application/json", typeof(object))]
    [OpenApiResponseWithBody(InternalServerError, "application/json", typeof(object))]
    [OpenApiIgnore]
    public async Task<HttpResponseData> DeleteTransferByReqNumber(
        [HttpTrigger(AuthorizationLevel.Function, "delete", Route = "transfers/{reqNumber}")]
        HttpRequestData req, string reqNumber)
    {
        try
        {
            var submitter = req.GetClaims();
            logger.LogInformation("Deleting a transfer item by request number");

            await using var dbContext = await transferDbContextFactory.CreateDbContextAsync();
            var response = await dbContext.Transfer!.SingleOrDefaultAsync(item => item.ReqNumber! == reqNumber);

            if (response == null || IsEmptyObject(response))
            {
                logger.LogWarning("Invalid Request Number {ReqNumber}", reqNumber);
                return await req.WriteErrorResponseAsync(BadRequest, $"Invalid Request Number {reqNumber}");
            }

            if (submitter.QId != null && response.SubmitterQId != submitter.QId)
            {
                logger.LogWarning(UnauthorizedQIdAccessTemplate, response.SubmitterQId);
                return await req.WriteUnauthorizedResponseAsync();
            }

            try
            {
                dbContext.Transfer!.Remove(response);
                await dbContext.SaveChangesAsync();
            }
            catch (DbUpdateException ex)
            {
                logger.LogError(ex, ErrorMessageTemplate, ex.Message);
                return ex.DefaultExceptionBehaviour(req, logger);
            }

            logger.LogInformation("Deleted Transfer By Request Number {ReqNumber}", response.ReqNumber);

            return await req.WriteOkResponseAsync("Deleted Successfully");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, ErrorMessageTemplate, ex.Message);
            return ex.DefaultExceptionBehaviour(req, logger);
        }
    }

    #endregion

    #region GetTransferReasonList

    /// <summary>
    /// Get the transfer reason list for the given submitter's QId
    /// </summary>
    /// <param name="req"></param>
    /// <returns></returns>
    [Function("GetTransferReasonList")]
    [OpenApiOperation(operationId: "GetTransferReasonList", tags: ["Transfer"],
        Summary = "Get Transfer Reason List", Description = " Get transfer reason list")]
    [OpenApiParameter("reasonCode", Description = "Transfer Reason Code", In = ParameterLocation.Query,
        Required = false, Type = typeof(string))]
    [OpenApiParameter("skip", Description = "Skip Records", In = ParameterLocation.Query, Required = false,
        Type = typeof(int))]
    [OpenApiParameter("take", Description = "Take Records", In = ParameterLocation.Query, Required = false,
        Type = typeof(int))]
    [OpenApiResponseWithBody(OK, "application/json", typeof(List<GetTransferReasonListResponse>),
        Description = "List Of Transfer Reasons")]
    [OpenApiResponseWithBody(BadRequest, "application/json", typeof(object))]
    [OpenApiResponseWithBody(NoContent, "application/json", typeof(object))]
    public async Task<HttpResponseData> GetTransferReasonList(
        [HttpTrigger(AuthorizationLevel.Function, "get", Route = "transfersreason")]
        HttpRequestData req)
    {
        try
        {
            var submitter = req.GetClaims();

            logger.LogInformation("Getting Transfer Reason List {QId}", submitter.QId);

            string reasonCode = req.GetCleanedQueryString("reasonCode");
            int skip = req.GetIntQueryParameter("skip");
            int take = req.GetIntQueryParameter("take");

            await using var dbContext = await transferDbContextFactory.CreateDbContextAsync();

            var query = dbContext.TransferReason?.AsQueryable();
            query.ThrowIfNull();

            if (!string.IsNullOrEmpty(reasonCode))
            {
                query = query.Where(item => item.ReasonCode == reasonCode);
            }

            var response = await query.Select(item => new GetTransferReasonListResponse
                    { ReasonCode = item.ReasonCode, ReasonEn = item.ReasonEn, ReasonAr = item.ReasonAr })
                .Skip(skip).Take(take).ToListAsync();

            if (response is not { Count: not 0 })
            {
                logger.LogInformation("Items not found");
                return await req.WriteNoContentResponseAsync();
            }

            logger.LogInformation("Returning Transfer Reason List");
            
            return await req.WriteOkResponseAsync(response);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, ErrorMessageTemplate, ex.Message);
            return ex.DefaultExceptionBehaviour(req, logger);
        }
    }

    #endregion
}