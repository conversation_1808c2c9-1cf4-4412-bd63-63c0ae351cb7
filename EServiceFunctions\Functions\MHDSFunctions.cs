#region Using References

using EServiceFunctions.Models.MHDS;
using EServiceFunctions.Models.MHDS.EDW;
using EServiceFunctions.RequestResponseModels.MHDS;
using EServiceFunctions.Helpers;

using static EServiceFunctions.MapperModels.MhdsMapper;

#endregion

namespace EServiceFunctions.Functions;

public class MhdsFunctions(
    IDbContextFactory<MHDSContext> mhdsContextFactory,
    IDbContextFactory<EDWDbContext> edwContextFactory,
    ILogger<MhdsFunctions> logger)
{
    // Logging message constants
    private const string UnauthorizedSubmitterAccessMsg = "Unauthorized ({SubmitterQId}) Access!!!";
    private const string UnauthorizedQidAccessMsg = "Unauthorized ({QId}) Access!!!";

    #region CheckInProcessMHDSByQID

    /// <param name="req"></param>
    /// <param name="qId"></param>
    /// <returns></returns>
    [Function("CheckInProcessMHDSByQID")]
    [OpenApiOperation(operationId: "CheckInProcessMHDSByQID", tags: ["MHDS"],
        Summary = "Check InProcess MHDS(s) By Requester's QID",
        Description = "Check inprocess MHDS(s) for the given requester's QId")]
    [OpenApiParameter("qId", Description = "Requester's QId", In = ParameterLocation.Path, Required = true,
        Type = typeof(long))]
    [OpenApiResponseWithBody(OK, "application/json", typeof(CheckInProcessMHDSResponse),
        Description = "Returns True Or False")]
    [OpenApiResponseWithBody(BadRequest, "application/json", typeof(object))]
    public async Task<HttpResponseData> CheckInProcessMhdsByQId(
        [HttpTrigger(AuthorizationLevel.Function, "get", Route = "mhds/{qId}/inprocess-validation")]
        HttpRequestData req, long qId)
    {
        try
        {
            logger.LogInformation("Checking InProcess MHDS(s) By Requester's QID {QId} - {Url}", qId, req.LogRequestedUrl());
            await using var dbContext = await mhdsContextFactory.CreateDbContextAsync();

            var query = dbContext.MHDSRequestDetails!
                .Join(dbContext.Status!, e => e.Status, s => s.Code, (mhds, status) => new { mhds, status })
                .AsQueryable();

            var isInProcessRequestExist = await query.AnyAsync(item =>
                item.mhds.QId == qId && item.status.Category! == InProcess);

            return await req.WriteOkResponseAsync(new
                CheckInProcessMHDSResponse
                {
                    IsInProcessExist = isInProcessRequestExist
                });
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error occurred while checking inprocess MHDS by QID {QId}", qId);
            return ex.DefaultExceptionBehaviour(req, logger);
        }
    }

    #endregion

    #region GetMHDSStatsByQID

    /// <summary>
    /// Get the mhds stats for the given submitter's QId
    /// </summary>
    /// <param name="req"></param>
    /// <param name="qId">Submitter's QId</param>
    /// <returns></returns>
    [Function("GetMHDSStatsByQID")]
    [OpenApiOperation(operationId: "GetMHDSStatsByQID", tags: ["MHDS"],
        Summary = "Get MHDS Stats By Submitter's QID",
        Description = "Get the mhds stats for the given submitter's QId")]
    [OpenApiParameter("qId", Description = "Submitter's QId", In = ParameterLocation.Path, Required = true,
        Type = typeof(long))]
    [OpenApiParameter("status",
        Description =
            "Please provide a valid status: InProcess {Submitted, PaymentReceived, ReturnedForPayment,} or Archived {Approved, Cancelled, Returned}.",
        In = ParameterLocation.Query, Required = false, Type = typeof(string))]
    [OpenApiResponseWithBody(OK, "application/json", typeof(GetMHDSStatsResponse))]
    [OpenApiResponseWithBody(BadRequest, "application/json", typeof(object))]
    public async Task<HttpResponseData> GetMhdsStatsByQId(
        [HttpTrigger(AuthorizationLevel.Function, "get", Route = "mhds/submitter-qid/{qId}/stats")]
        HttpRequestData req, long qId)
    {
        try
        {
            if (!req.IsAuthorized(qId))
            {
                logger.LogWarning(UnauthorizedQidAccessMsg, qId);
                return await req.WriteUnauthorizedResponseAsync();
            }

            logger.LogInformation("Getting MHDS Stats By Submitter's QID {QId} - {Url}", qId, req.LogRequestedUrl());

            await using var dbContext = await mhdsContextFactory.CreateDbContextAsync();

            logger.LogInformation("Getting MHDS Stats By Submitter's QID {QId}", qId);

            string assignmentStatus = req.GetCleanedQueryString("status");

            var query = dbContext.MHDSRequestDetails!
                .Join(dbContext.Status!, e => e.Status, s => s.Code, (mhds, status) => new { mhds, status })
                .AsQueryable();

            query = query.Where(item => item.mhds.SubmitterQId == qId);

            if (!string.IsNullOrWhiteSpace(assignmentStatus))
            {
                query = query.Where(item =>
                    item.status.Category! == assignmentStatus || item.mhds.Status! == assignmentStatus);
            }

            var response = await query.CountAsync();

            return await req.WriteOkResponseAsync(new GetMHDSStatsResponse { Count = response });
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error occurred while getting assignment stats by QID {QId}", qId);
            return ex.DefaultExceptionBehaviour(req, logger);
        }
    }

    #endregion

    #region GetMHDSListByHealthCardNumber

    /// <summary>
    /// RetrieveMedicineList: Retrieve a List of Refill medicines that are due for this cycle. For User and
    /// Dependents. To be called on first screen. FE to send the list of HCNumbers.
    /// </summary>
    [Function("GetMHDSListByHCNumber")]
    [OpenApiOperation(operationId: "GetMHDSListByHCNumber", tags: ["MHDS"],
        Summary = "Retrieve MHDS Medicine List By Health Card Number",
        Description = "Retrieve the MHDS medicine list for the given Health Card Number")]
    [OpenApiRequestBody("application/json", typeof(RequestMHDSMedicineList))]
    [OpenApiResponseWithBody(OK, "application/json", typeof(List<GetMedicineList>),
        Description = "List of Refillable MedicineInformation")]
    [OpenApiResponseWithBody(BadRequest, "application/json", typeof(object))]
    [OpenApiResponseWithBody(NoContent, "application/json", typeof(object))]
    public async Task<HttpResponseData> GetMhdsListByHcNumber(
        [HttpTrigger(AuthorizationLevel.Function, "post", Route = "mhds/medicinelist")]
        HttpRequestData req)
    {
        try
        {
            logger.LogInformation("Getting MHDS List By Health Card Number - {Url}", req.LogRequestedUrl());

            await using var edwDbContext = await edwContextFactory.CreateDbContextAsync();
            await using var dbContext = await mhdsContextFactory.CreateDbContextAsync();

            string requestBody = await new StreamReader(req.Body).ReadToEndAsync();
            requestBody.ThrowIfNullOrWhiteSpace();
            var request = DeserializeObject<RequestMHDSMedicineList>(requestBody);
            request.ThrowIfNullOrDefault();
            var filteredMedicineListFromEdw = new List<GetMedicineList>();
            request.HealthCardNumbers?.ThrowIfNull();

            if (request.HealthCardNumbers is null || request.HealthCardNumbers.Count == 0)
            {
                return filteredMedicineListFromEdw.Count != 0
                    ? await req.WriteOkResponseAsync(filteredMedicineListFromEdw)
                    : await req.WriteNoContentResponseAsync();
            }

            var medicineListFromEdw = new List<GetMedicineList>();
            foreach (var hcNumber in request.HealthCardNumbers)
            {
                var getMedicineObject = new GetMedicineList();
                var medicinesFromEdw = await GetMedicineListFromEdw(edwDbContext, req, hcNumber);
                var medicinesFromEServ = await GetMedicineListFromEServ(dbContext, hcNumber);

                var removed = medicinesFromEdw.RemoveAll(c => medicinesFromEServ
                    .Any(a => a.MedicineName?.Trim() == c.MedicineName?.Trim()));
                logger.LogInformation("Totally Filtered Medicines from EDW : {Removed}", removed);

                if (medicinesFromEdw.Count == 0) continue;

                getMedicineObject.MedicineInformation = medicinesFromEdw;
                medicineListFromEdw.Add(getMedicineObject);
            }

            return medicineListFromEdw.Count != 0
                ? await req.WriteOkResponseAsync(medicineListFromEdw)
                : await req.WriteNoContentResponseAsync();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error occurred while getting MHDS list by health card number");
            return ex.DefaultExceptionBehaviour(req, logger);
        }
    }

    private static async Task<List<MedicineInformation>> GetMedicineListFromEdw(EDWDbContext edwDbContext,
        HttpRequestData req,
        string hcNumber)
    {
        var medicineListFromEdw = new List<MedicineInformation>();
        var env = GetEnvironmentVariable("EDWEnv") ?? "STG";

        if (env.Equals("PROD", OrdinalIgnoreCase) || req.Headers.Contains("AssumeProd"))
        {
            var medicationRefillDetailsFromEdwProd = await edwDbContext
                .MedicationRefillDetailsProd!.Where(x => x.HcNumber == hcNumber)
                .AsNoTracking().ToListAsync();

            if (medicationRefillDetailsFromEdwProd.Count == 0)
            {
                return medicineListFromEdw;
            }

            medicineListFromEdw.AddRange(medicationRefillDetailsFromEdwProd.Select(medication => new MedicineInformation
            {
                MedicineName = medication.PrescribedMedication,
                PrescribedDate = medication.PrescriptionDate,
                SupplyDuration = medication.SupplyDuration.ToString(),
                LastDispenseDate = medication.LastDispenseDate,
                LastDispensedLocation = medication.LastDispenseFacCode,
                OrderByName = medication.PrescribedByNameEng,
                PrescriptionRefillDueDate = medication.PrescriptionRefillDueDate,
                PrescriptionOrderId = medication.PrescriptionOrderId.ToString()
            }));
        }
        else
        {
            var medicationRefillDetailsFromEdwStg = await edwDbContext
                .MedicationRefillDetailsStg!.Where(x => x.HcNumber == hcNumber)
                .AsNoTracking().ToListAsync();

            if (medicationRefillDetailsFromEdwStg.Count == 0)
            {
                return medicineListFromEdw;
            }

            medicineListFromEdw.AddRange(medicationRefillDetailsFromEdwStg.Select(medication => new MedicineInformation
            {
                MedicineName = medication.PrescribedMedication,
                PrescribedDate = medication.PrescriptionDate,
                SupplyDuration = medication.SupplyDuration.ToString(),
                LastDispenseDate = medication.LastDispenseDate,
                LastDispensedLocation = medication.LastDispenseFacCode,
                OrderByName = medication.PrescribedByNameEng,
                PrescriptionRefillDueDate = medication.PrescriptionRefillDueDate,
                PrescriptionOrderId = medication.PrescriptionOrderId.ToString()
            }));
        }

        return medicineListFromEdw;
    }

    private async Task<List<MedicineInformation>> GetMedicineListFromEServ(MHDSContext dbContext, string hcNumber)
    {
        var medicineListFromEServ = new List<MedicineInformation>();

        var mhdsRequestDetails = await dbContext
            .MHDSRequestDetails!
            .Where(c => c.HcNumber == hcNumber
                        && (c.Status == Submitted
                            || c.Status == PaymentReceived
                            || c.Status == ReturnedForPayment
                            || c.Status == InProcess))
            .AsNoTracking().ToListAsync();

        if (mhdsRequestDetails.Count == 0)
        {
            return medicineListFromEServ;
        }

        var requestNumbers = mhdsRequestDetails.Select(rd => rd.ReqNumber).ToList();

        var medicineInfoListFromEServ = await dbContext
            .MHDSRequestMedicineList!.Where(c => requestNumbers.Contains(c.RequestNumber))
            .AsNoTracking().ToListAsync();

        medicineListFromEServ.AddRange(medicineInfoListFromEServ.Select(medicineInfoFromEServ =>
            new MedicineInformation
            {
                MedicineName = medicineInfoFromEServ.MedicineName,
                PrescribedDate = medicineInfoFromEServ.PrescribedDate,
                SupplyDuration = medicineInfoFromEServ.SupplyDuration,
                LastDispenseDate = medicineInfoFromEServ.LastDispenseDate,
                LastDispensedLocation = medicineInfoFromEServ.LastDispensedLocation,
                OrderByName = medicineInfoFromEServ.OrderByName,
                PrescriptionRefillDueDate = medicineInfoFromEServ.PrescriptionDueDate,
                PrescriptionOrderId = medicineInfoFromEServ.PrescriptionOrderId,
                AttachId = medicineInfoFromEServ.AttachId
            }));

        return medicineListFromEServ;
    }

    #endregion

    #region CreateMHDSRequestAsync

    /// <summary>
    /// CreateMedicineDeliveryRequest: Create a refill request for one user with single/multiple orders.
    /// For each user, a new request has to be submitted.
    /// </summary>
    [Function("CreateMHDS")]
    [OpenApiOperation(operationId: "CreateMHDS", tags: ["MHDS"], Summary = "Create MHDS",
        Description = "Create new MHDS request")]
    [OpenApiRequestBody("application/json", typeof(CreateMHDSRequest))]
    [OpenApiResponseWithBody(Created, "application/json", typeof(MHDSRequestDetails))]
    [OpenApiResponseWithBody(BadRequest, "application/json", typeof(object))]
    [OpenApiResponseWithBody(InternalServerError, "application/json", typeof(object))]
    public async Task<HttpResponseData> CreateMhdsRequestAsync(
        [HttpTrigger(AuthorizationLevel.Function, "post", Route = "mhds")]
        HttpRequestData req)
    {
        try
        {
            var requestOriginSource = req.TryGetHeader(RequestOrigin);

            if (string.IsNullOrWhiteSpace(requestOriginSource))
            {
                return await req.WriteErrorResponseAsync(BadRequest, "Required 'X-RequestOrigin' header value");
            }

            string requestBody = await new StreamReader(req.Body).ReadToEndAsync();

            var request = DeserializeObject<CreateMHDSRequest>(requestBody);
            if (request is null)
            {
                logger.LogWarning("Invalid request body. Please try again");
                return await req.WriteErrorResponseAsync(BadRequest, "Invalid request body. Please try again.");
            }
            
            if (!req.IsAuthorized(request.SubmitterQId))
            {
                logger.LogWarning(UnauthorizedSubmitterAccessMsg, request.SubmitterQId);
                return await req.WriteUnauthorizedResponseAsync();
            }
            
            var healthCardValidation = await ValidateHealthCardNumberAsync(
                request.HCNumber, request.QId, "MHDS", req, logger);

            if (!healthCardValidation.IsValid)
            {
                return healthCardValidation.ErrorResponse!;
            }

            logger.LogInformation("Creating an MHDS request...");

            await using var dbContext = await mhdsContextFactory.CreateDbContextAsync();

            // Check for existing in-process MHDS requests
            var existingInProcessQuery = dbContext.MHDSRequestDetails!
                .Join(dbContext.Status!, m => m.Status, s => s.Code, (mhds, status) => new { mhds, status })
                .AsQueryable();

            var isInProcessRequestExist = await existingInProcessQuery.AnyAsync(item =>
                item.mhds.QId == request.QId && item.status.Category! == InProcess);

            if (isInProcessRequestExist)
            {
                logger.LogWarning("Duplicate MHDS request detected for QId {QId}", request.QId);
                return await req.WriteErrorResponseAsync(BadRequest, "An in-process MHDS request already exists for this applicant.");
            }

            MHDSRequestDetails create = new()
            {
                ReqNumber = RandomPassword(),
                QId = request.QId,
                SubmitterQId = request.SubmitterQId,
                FNameEn = request.FNameEn,
                FNameAr = request.FNameAr,
                MNameEn = request.MNameEn,
                MNameAr = request.MNameAr,
                LNameEn = request.LNameEn,
                LNameAr = request.LNameAr,
                HcNumber = request.HCNumber,
                CurrentAssignedHc = request.CurrentAssignedHC,
                Dob = ToDateTime(request.Dob),
                Nationality = request.Nationality,
                UNo = request.UNo,
                BNo = request.BNo,
                SNo = request.SNo,
                ZNo = request.ZNo,
                IsGisAddressManualyEntered = request.IsGisAddressManualyEntered,
                SubmitterMobile = request.SubmitterMobile,
                SecondaryPhoneMobile = request.SecondaryMobNo,
                SubmitterEmail = request.SubmitterEmail,
                SubmittedAt = GetCurrentTime(),
                CreatedAt = GetCurrentTime(),
                CreateSource = requestOriginSource,
                Consent = request.Consent,
                Action = request.Action,
                Status = Submitted
            };

            var medicineList = new List<MHDSRequestMedicineList>();

            foreach (var medicineInfo in request.MedInfo!)
            {
                var medicine = new MHDSRequestMedicineList
                {
                    RequestNumber = create.ReqNumber,
                    MedicineName = medicineInfo.MedicineName,
                    PrescriptionOrderId = medicineInfo.PrescriptionOrderId,
                    PrescribedDate = medicineInfo.PrescribedDate,
                    PrescriptionDueDate = medicineInfo.PrescriptionRefillDueDate,
                    LastDispenseDate = medicineInfo.LastDispenseDate,
                    SupplyDuration = medicineInfo.SupplyDuration,
                    LastDispensedLocation = medicineInfo.LastDispensedLocation,
                    OrderByName = medicineInfo.OrderByName,
                    AttachId = medicineInfo.AttachId
                };
                medicineList.Add(medicine);
            }

            await dbContext.MHDSRequestMedicineList!.AddRangeAsync(medicineList);
            await dbContext.MHDSRequestDetails!.AddAsync(create);
            var result = await dbContext.SaveChangesAsync();

            logger.LogInformation("Created A New MHDS With Request Number {ReqNumber}", create.ReqNumber);

            if (result > 0)
            {
                return await req.WriteOkResponseAsync(create, Created);
            }

            return await req.WriteErrorResponseAsync(BadRequest, "Invalid Request");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error occurred while creating MHDS request");
            return ex.DefaultExceptionBehaviour(req, logger);
        }
    }

    #endregion

    #region UpdateMHDSByReqNumber

    /// <summary>
    /// UpdateMedicineDeliveryRequest: Patch operation to allow request cancellation (MHDSStatus update)
    /// and GIS information update. 
    /// </summary>
    [Function("UpdateMHDSByReqNumber")]
    [OpenApiOperation(operationId: "UpdateMHDSByReqNumber", tags: ["MHDS"],
        Summary = "Update MHDS By ReqNumber", Description = "Update MHDS item for the given request number")]
    [OpenApiRequestBody("application/json", typeof(UpdateMHDSRequest))]
    [OpenApiParameter("reqNumber", Description = "MHDS Request Number", In = ParameterLocation.Path, Required = true,
        Type = typeof(string))]
    [OpenApiResponseWithBody(BadRequest, "application/json", typeof(object))]
    [OpenApiResponseWithBody(OK, "application/json", typeof(MHDSRequestDetails))]
    [OpenApiResponseWithBody(InternalServerError, "application/json", typeof(object))]
    public async Task<HttpResponseData> UpdateMhdsByReqNumber(
        [HttpTrigger(AuthorizationLevel.Function, "put", Route = "mhds/{reqNumber}")]
        HttpRequestData req, string reqNumber)
    {
        try
        {
            logger.LogInformation("Updating MHDS By Request Number {ReqNumber} - {Url}", reqNumber, req.LogRequestedUrl());
            var requestOriginSource = req.TryGetHeader(RequestOrigin);

            if (string.IsNullOrWhiteSpace(requestOriginSource))
            {
                return await req.WriteErrorResponseAsync(BadRequest, "Required 'X-RequestOrigin' header value");
            }

            using var streamReader = new StreamReader(req.Body);

            string requestBody = await streamReader.ReadToEndAsync();
            var request = DeserializeObject<UpdateMHDSRequest>(requestBody);
            if (request is null || IsEmptyObject(request))
            {
                logger.LogWarning("Invalid request body. Please try again");
                return await req.WriteErrorResponseAsync();
            }
            
            if (!req.IsAuthorized(request.SubmitterQId))
            {
                logger.LogWarning(UnauthorizedSubmitterAccessMsg, request.SubmitterQId);
                return await req.WriteUnauthorizedResponseAsync();
            }

            await using var dbContext = await mhdsContextFactory.CreateDbContextAsync();

            MHDSRequestDetails? response = await dbContext.MHDSRequestDetails
                .FirstOrDefaultAsync(i => i.ReqNumber! == reqNumber
                                          && i.SubmitterQId == request.SubmitterQId);

            if (response?.ReqNumber is null)
            {
                logger.LogWarning("Invalid Req Number {ReqNumber}", reqNumber);
                return await req.WriteErrorResponseAsync(BadRequest, $"Invalid Req Number {reqNumber}");
            }
            
            var healthCardValidation = await ValidateHealthCardNumberAsync(
                response.HcNumber, request.QId, "MHDS", req, logger);

            if (!healthCardValidation.IsValid)
            {
                return healthCardValidation.ErrorResponse!;
            }

            response.QId = request.QId;
            response.SubmitterQId = request.SubmitterQId;
            response.IsGisAddressManualyEntered = request.IsGisAddressManualyEntered;

            if (request.IsGisAddressManualyEntered)
            {
                response.UNo = request.UNo;
                response.BNo = request.BNo;
                response.SNo = request.SNo;
                response.ZNo = request.ZNo;
            }

            if (!dbContext.ChangeTracker.HasChanges())
            {
                return await req.WriteOkResponseAsync(response);
            }

            response.UpdatedAt = GetCurrentTime();
            response.UpdateSource = requestOriginSource;

            dbContext.MHDSRequestDetails!.Update(response);
            int result = await dbContext.SaveChangesAsync();
            logger.LogInformation("Updated A New MHDS With Request Number {ReqNumber}", response.ReqNumber);

            return result > 0
                ? await req.WriteOkResponseAsync(response)
                : await req.WriteErrorResponseAsync(BadRequest, "Invalid Request");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error occurred while updating MHDS by reqNumber {ReqNumber}", reqNumber);
            return ex.DefaultExceptionBehaviour(req, logger);
        }
    }

    #endregion

    #region GetMHDSListByQID

    /// <summary>
    /// GetMedicineDeliveryRequestList: Get the list of the Refill requests for the User and dependents.
    /// Fields will be , ReqNumber, Username, Status,
    /// </summary>
    [Function("GetMHDSListByQID")]
    [OpenApiOperation(operationId: "GetMHDSListByQID", tags: ["MHDS"],
        Summary = "Get MHDS List By Submitter's QID", Description = " Get the MHDS list for the given submitter's QId")]
    [OpenApiParameter("qId", Description = "Submitter's QId", In = ParameterLocation.Path, Required = true,
        Type = typeof(long))]
    [OpenApiParameter("status",
        Description =
            "Please provide a valid status: InProcess {Submitted, PaymentReceived, ReturnedForPayment,} or Archived {Approved, Cancelled, Returned}.",
        In = ParameterLocation.Query, Required = false, Type = typeof(string))]
    [OpenApiParameter("skip", Description = "Skip Records", In = ParameterLocation.Query, Required = false,
        Type = typeof(int))]
    [OpenApiParameter("take", Description = "Take Records", In = ParameterLocation.Query, Required = false,
        Type = typeof(int))]
    [OpenApiResponseWithBody(OK, "application/json", typeof(List<MHDSRequestList>),
        Description =
            "Type of requests to return viz. ReturnedForPayment, PaymentReceived, Submitted, Approved, Cancelled, Returned.")]
    [OpenApiResponseWithBody(BadRequest, "application/json", typeof(object))]
    [OpenApiResponseWithBody(NoContent, "application/json", typeof(object))]
    public async Task<HttpResponseData> GetMhdsListByQId(
        [HttpTrigger(AuthorizationLevel.Function, "get", Route = "mhds/submitter-qid/{qId}")]
        HttpRequestData req, long qId)
    {
        try
        {
            if (!req.IsAuthorized(qId))
            {
                logger.LogWarning(UnauthorizedQidAccessMsg, qId);
                return await req.WriteUnauthorizedResponseAsync();
            }
            
            logger.LogInformation("Getting MHDS List By Submitter's QID {QId} - {Url}", qId, req.LogRequestedUrl());
            logger.LogInformation("Getting MHDS List By Submitter's QID {QId}", qId);

            string assignmentStatus = req.GetCleanedQueryString("status");
            int skip = req.GetIntQueryParameter("skip");
            int take = req.GetIntQueryParameter("take");

            await using var dbContext = await mhdsContextFactory.CreateDbContextAsync();

            var query = dbContext.MHDSRequestDetails!.Join(dbContext.Status!, e => e.Status, s => s.Code,
                    (mhds, status) => new { mhds, status }).OrderBy(item => item.mhds.Id)
                .AsQueryable().AsNoTracking();

            query = query.Where(item => item.mhds.SubmitterQId == qId);

            if (!string.IsNullOrWhiteSpace(assignmentStatus))
            {
                query = query.Where(item =>
                    item.status.Category! == assignmentStatus || item.mhds.Status! == assignmentStatus);
            }

            var response = await query.Select(item => new MHDSRequestList
            {
                QId = item.mhds.QId,
                ReqNumber = item.mhds.ReqNumber!,
                FNameEn = item.mhds.FNameEn,
                MNameEn = item.mhds.MNameEn,
                LNameEn = item.mhds.LNameEn,
                FNameAr = item.mhds.FNameAr,
                MNameAr = item.mhds.MNameAr,
                LNameAr = item.mhds.LNameAr,
                Status = item.mhds.StatusNavigation!.Code,
                StatusDescriptionEn = item.mhds.StatusNavigation.DescriptionEn,
                StatusDescriptionAr = item.mhds.StatusNavigation.DescriptionAr,
                SubmittedAt = item.mhds.SubmittedAt.ToUtcString()
            }).OrderBy(p => p.Status).Skip(skip).Take(take).ToListAsync();

            if (response.Count == 0)
            {
                logger.LogInformation("Items not found for a given QID {QId} and Status {AssignmentStatus}", qId, assignmentStatus);
                return await req.WriteNoContentResponseAsync();
            }

            return await req.WriteOkResponseAsync(response);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error occurred while getting MHDS list by QID {QId}", qId);
            return ex.DefaultExceptionBehaviour(req, logger);
        }
    }

    #endregion

    #region GetMHDSItemByReqNumber

    /// <summary>
    /// GetMedicineDeliveryRequestDetails: Get details of the Refill request. PrescriptionOrderId, Name, Status
    /// </summary>
    [ExcludeFromCodeCoverage]
    [Function("GetMHDSItemByReqNumber")]
    [OpenApiOperation(operationId: "GetMHDSItemByReqNumber", tags: ["MHDS"],
        Summary = "Get MHDS Item By ReqNumber", Description = "Get MHDS item for the given request number")]
    [OpenApiParameter("reqNumber", Description = "MHDS Request Number", In = ParameterLocation.Path, Required = true,
        Type = typeof(string))]
    [OpenApiResponseWithBody(OK, "application/json", typeof(GetMHDSItemResponse))]
    [OpenApiResponseWithBody(BadRequest, "application/json", typeof(object))]
    [OpenApiResponseWithBody(NoContent, "application/json", typeof(object))]
    public async Task<HttpResponseData> GetMhdsItemByReqNumber(
        [HttpTrigger(AuthorizationLevel.Function, "get", Route = "mhds/{reqNumber}")]
        HttpRequestData req, string reqNumber)
    {
        try
        {
            logger.LogInformation("Getting MHDS Item By Request Number {ReqNumber} - {Url}", reqNumber, req.LogRequestedUrl());

            var submitter = req.GetClaims();

            if (string.IsNullOrWhiteSpace(reqNumber))
            {
                logger.LogWarning("Invalid Req Number {ReqNumber}", reqNumber);
                return await req.WriteErrorResponseAsync(BadRequest, $"Invalid Req Number {reqNumber}");
            }

            logger.LogInformation("Getting MHDS Item By Request Number {ReqNumber}", reqNumber);

            await using var dbContext = await mhdsContextFactory.CreateDbContextAsync();

            MHDSRequestDetails? response = await dbContext.MHDSRequestDetails!.Include(s => s.StatusNavigation)
                .FirstOrDefaultAsync(item => item.ReqNumber! == reqNumber);

            if (response is null || IsEmptyObject(response))
            {
                logger.LogInformation("Item not found");
                return await req.WriteNoContentResponseAsync();
            }

            if (submitter.QId != null && response.SubmitterQId != submitter.QId)
            {
                logger.LogWarning(UnauthorizedSubmitterAccessMsg, response.SubmitterQId);
                return await req.WriteUnauthorizedResponseAsync();
            }

            var getResponse = MapMhdsRequestDetailsToGetMhdsItemResponse(response);
            getResponse.Status = response.StatusNavigation!.Code;
            getResponse.StatusDescriptionEn = response.StatusNavigation.DescriptionEn;
            getResponse.StatusDescriptionAr = response.StatusNavigation.DescriptionAr;

            var medicineList = await dbContext.MHDSRequestMedicineList!.Where(x => x.RequestNumber == reqNumber)
                .ToListAsync();

            var mList = medicineList.Select(medicine => new MedicineInformation
                {
                    MedicineName = medicine.MedicineName,
                    PrescribedDate = medicine.PrescribedDate,
                    LastDispenseDate = medicine.LastDispenseDate,
                    SupplyDuration = medicine.SupplyDuration,
                    PrescriptionRefillDueDate = medicine.PrescriptionDueDate,
                    LastDispensedLocation = medicine.LastDispensedLocation,
                    OrderByName = medicine.OrderByName,
                    PrescriptionOrderId = medicine.PrescriptionOrderId,
                    AttachId = medicine.AttachId
                })
                .ToList();

            getResponse.MedicineList = mList;

            return await req.WriteOkResponseAsync(getResponse);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error occurred while getting MHDS item by reqNumber {ReqNumber}", reqNumber);
            return ex.DefaultExceptionBehaviour(req, logger);
        }
    }

    #endregion

    #region DeleteMHDSByReqNumber

    /// <summary>
    /// Delete MHDS for the given request number
    /// Note: Only for internal use
    /// </summary>
    [Function("DeleteMHDSByReqNumber")]
    [OpenApiOperation(operationId: "DeleteMHDSByReqNumber", tags: ["MHDS"],
        Summary = "Delete MHDS By ReqNumber", Description = "Delete MHDS item for the given request number")]
    [OpenApiParameter("reqNumber", Description = "MHDS Request Number", In = ParameterLocation.Path, Required = true,
        Type = typeof(string))]
    [OpenApiResponseWithBody(BadRequest, "application/json", typeof(object))]
    [OpenApiResponseWithBody(NoContent, "application/json", typeof(object))]
    [OpenApiResponseWithBody(InternalServerError, "application/json", typeof(object))]
    [OpenApiIgnore]
    public async Task<HttpResponseData> DeleteMhdsByReqNumber(
        [HttpTrigger(AuthorizationLevel.Function, "delete", Route = "mhds/{reqNumber}")]
        HttpRequestData req, string reqNumber)
    {
        try
        {
            logger.LogInformation("Deleting MHDS By Request Number {ReqNumber} - {Url}", reqNumber, req.LogRequestedUrl());
            var submitter = req.GetClaims();

            await using var dbContext = await mhdsContextFactory.CreateDbContextAsync();

            var response = await dbContext.MHDSRequestDetails!
                .SingleOrDefaultAsync(item => item.ReqNumber! == reqNumber);

            var medicineListResponse = await dbContext.MHDSRequestMedicineList!
                .Where(item => item.RequestNumber! == reqNumber).ToListAsync();

            if (response == null || medicineListResponse.Count == 0)
            {
                logger.LogWarning("No Request Found for Given Request Number");
                return await req.WriteErrorResponseAsync(BadRequest,
                    "No Request Found for Given Request Number");
            }

            if (submitter.QId is not null && response.SubmitterQId != submitter.QId)
            {
                logger.LogWarning(UnauthorizedSubmitterAccessMsg, response.SubmitterQId);
                return await req.WriteUnauthorizedResponseAsync();
            }

            logger.LogInformation("Deleting An MHDS request...");

            try
            {
                dbContext.MHDSRequestMedicineList!.RemoveRange(medicineListResponse);
                dbContext.MHDSRequestDetails!.Remove(response);
                await dbContext.SaveChangesAsync();
            }
            catch (DbUpdateException e)
            {
                logger.LogError(e, "Error: {ErrorMessage}", e.InnerException?.Message);
            }

            return await req.WriteNoContentResponseAsync();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error occurred while deleting MHDS by reqNumber {ReqNumber}", reqNumber);
            return ex.DefaultExceptionBehaviour(req, logger);
        }
    }

    #endregion
}