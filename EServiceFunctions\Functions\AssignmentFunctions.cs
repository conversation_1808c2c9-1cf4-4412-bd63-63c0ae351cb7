using EServiceFunctions.Models.Assignment;
using EServiceFunctions.RequestResponseModels.Assignment;
using static EServiceFunctions.MapperModels.AssignmentMapper;

using static Microsoft.OpenApi.Models.ParameterLocation;

using Exception = System.Exception;

namespace EServiceFunctions.Functions;

public static class AssignmentConstants
{
    public const string UnauthorizedAccessMessage = "Unauthorized ({QId}) Access!!!";
    public const string InvalidRequestBodyMessage = "Invalid request body. Please try again.";
    public const string InvalidReqNumberMessage = "Invalid Req Number {0}";
}

public class AssignmentFunctions(
    IDbContextFactory<AssignmentContext> dbContextFactory,
    ILogger<AssignmentFunctions> logger)
{
    #region GetAssignmentListByQID

    /// <summary>
    /// Get the assignment list for the given submitter's QId
    /// </summary>
    /// <param name="req"></param>
    /// <param name="qId">Submitter's QId</param>
    /// <returns></returns>
    [Function("GetAssignmentListByQID")]
    [OpenApiOperation(operationId: "GetAssignmentListByQID", tags: ["Assignment"],
        Summary = "Get Assignment List By Submitter's QID",
        Description = " Get the assignment list for the given submitter's QId")]
    [OpenApiParameter("qId", Description = "Submitter's QId", In = ParameterLocation.Path, Required = true,
        Type = typeof(long))]
    [OpenApiParameter("status",
        Description =
            "Please provide a valid status: InProcess {Submitted, Rework, Reworked,} or Archived {Approved, Cancelled, CancelledByEServ}.",
        In = Query, Required = false, Type = typeof(string))]
    [OpenApiParameter("skip", Description = "Skip Records", In = Query, Required = false,
        Type = typeof(int))]
    [OpenApiParameter("take", Description = "Take Records", In = Query, Required = false,
        Type = typeof(int))]
    [OpenApiResponseWithBody(OK, "application/json", typeof(List<GetAssignmentListResponse>),
        Description =
            "Type of requests to return viz. Submitted, Cancelled, CancelledByEServ, Rework, Approved, Reworked.")]
    [OpenApiResponseWithBody(BadRequest, "application/json", typeof(ErrorResponse))]
    [OpenApiResponseWithBody(NoContent, "application/json", typeof(object))]
    [OpenApiResponseWithBody(Unauthorized, "application/json", typeof(object))]
    public async Task<HttpResponseData> GetAssignmentListByQId(
        [HttpTrigger(AuthorizationLevel.Function, "get", Route = "assignments/submitter-qid/{qId}")]
        HttpRequestData req, long qId)
    {
        try
        {
            logger.LogInformation("Getting Assignment List By Submitter's QId {QId}", qId);

            if (!req.IsAuthorized(qId))
            {
                logger.LogWarning(AssignmentConstants.UnauthorizedAccessMessage, qId);
                return await req.WriteUnauthorizedResponseAsync();
            }

            string assignmentStatus = req.GetCleanedQueryString("status");
            int skip = req.GetIntQueryParameter("skip");
            int take = req.GetIntQueryParameter("take");

            await using var dbContext = await dbContextFactory.CreateDbContextAsync();

            var query = dbContext.Assignment!.AsNoTracking()
                .Join(dbContext.Status!, e => e.Status, s =>
                    s.Code, (assignment, status) => new { assignment, status })
                .AsQueryable();

            query = query.Where(item => item.assignment.SubmitterQId == qId);

            if (!string.IsNullOrWhiteSpace(assignmentStatus))
            {
                query = query.Where(item =>
                    item.status.Category == assignmentStatus || item.assignment.Status == assignmentStatus);
            }

            var response = await query.Select(item =>
                new GetAssignmentListResponse
                {
                    QId = item.assignment.QId,
                    ReqNumber = item.assignment.ReqNumber,
                    FNameEn = item.assignment.FNameEn,
                    MNameEn = item.assignment.MNameEn,
                    LNameEn = item.assignment.LNameEn,
                    FNameAr = item.assignment.FNameAr,
                    MNameAr = item.assignment.MNameAr,
                    LNameAr = item.assignment.LNameAr,
                    Status = item.assignment.StatusNavigation!.Code,
                    StatusDescriptionEn = item.assignment.StatusNavigation.DescriptionEn,
                    StatusDescriptionAr = item.assignment.StatusNavigation.DescriptionAr,
                    SubmittedAt = item.assignment.SubmittedAt.ToUtcString()
                }).OrderBy(p => p.QId).Skip(skip).Take(take).ToListAsync();

            if (response.Count == 0)
            {
                logger.LogInformation("Items not found for a given QID {QId} and Status {Status}", qId, assignmentStatus);
                return await req.WriteNoContentResponseAsync();
            }

            logger.LogInformation("Returning Assignment List ({Count}) By Submitter's QID {QId}", response.Count, qId);

            return await req.WriteOkResponseAsync(response);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error occurred while getting assignment list by QID {QId}", qId);
            return ex.DefaultExceptionBehaviour(req, logger);
        }
    }

    #endregion

    #region GetAssignmentStatsByQID

    /// <summary>
    /// Get the assignment stats for the given submitter's QId
    /// </summary>
    /// <param name="req"></param>
    /// <param name="qId">Submitter's QId</param>
    /// <returns></returns>
    [Function("GetAssignmentStatsByQID")]
    [OpenApiOperation(operationId: "GetAssignmentStatsByQID", tags: ["Assignment"],
        Summary = "Get Assignment Stats By Submitter's QID",
        Description = "Get the assignment stats for the given submitter's QId")]
    [OpenApiParameter("qId", Description = "Submitter's QId", In = ParameterLocation.Path, Required = true,
        Type = typeof(long))]
    [OpenApiParameter("status",
        Description =
            "Please provide a valid status: InProcess {Submitted, Rework, Reworked,} or Archived {Approved, Cancelled, CancelledByEServ}.",
        In = Query, Required = false, Type = typeof(string))]
    [OpenApiResponseWithBody(OK, "application/json", typeof(GetAssignmentStatsResponse))]
    [OpenApiResponseWithBody(BadRequest, "application/json", typeof(ErrorResponse))]
    [OpenApiResponseWithBody(Unauthorized, "application/json", typeof(object))]
    public async Task<HttpResponseData> GetAssignmentStatsByQId(
        [HttpTrigger(AuthorizationLevel.Function, "get", Route = "assignments/submitter-qid/{qId}/stats")]
        HttpRequestData req, long qId)
    {
        try
        {
            if (!req.IsAuthorized(qId))
            {
                logger.LogWarning(AssignmentConstants.UnauthorizedAccessMessage, qId);
                return await req.WriteUnauthorizedResponseAsync();
            }

            logger.LogInformation("Getting Assignment Stats By Submitter's QID {QId}", qId);
            string assignmentStatus = req.GetCleanedQueryString("status");

            await using var dbContext = await dbContextFactory.CreateDbContextAsync();

            var query = dbContext.Assignment!.AsNoTracking()
                .Join(dbContext.Status!, e => e.Status, s => s.Code,
                    (assignment, status) => new
                    {
                        assignment, status
                    }).AsQueryable();

            query = query.Where(item => item.assignment.SubmitterQId == qId);

            if (!string.IsNullOrWhiteSpace(assignmentStatus))
            {
                query = query.Where(
                    item => item.status.Category! == assignmentStatus || item.assignment.Status! == assignmentStatus);
            }

            var response = await query.CountAsync();

            logger.LogInformation("Returning Assignments count {Count} By Submitter's QID {QId} & Status {Status}", response, qId, assignmentStatus);

            return await req.WriteOkResponseAsync(new GetAssignmentStatsResponse
            {
                Count = response
            });
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error occurred while getting assignment stats by QID {QId}", qId);
            return ex.DefaultExceptionBehaviour(req, logger);
        }
    }

    #endregion

    #region CheckInProcessAssignmentByQID

    /// <summary>
    /// Check InProcess Assignment(s) By Requester's QID
    /// </summary>
    /// <param name="req"></param>
    /// <param name="qId"></param>
    /// <returns></returns>
    [Function("CheckInProcessAssignmentByQID")]
    [OpenApiOperation(operationId: "CheckInProcessAssignmentByQID", tags: ["Assignment"],
        Summary = "Check InProcess Assignment(s) By Requester's QID",
        Description = "Check inprocess Assignment(s) for the given requester's QId")]
    [OpenApiParameter("qId", Description = "Requester's QId", In = ParameterLocation.Path, Required = true,
        Type = typeof(long))]
    [OpenApiResponseWithBody(OK, "application/json", typeof(CheckInProcessAssignmentResponse),
        Description = "Returns True Or False")]
    [OpenApiResponseWithBody(BadRequest, "application/json", typeof(ErrorResponse))]
    [OpenApiResponseWithBody(Unauthorized, "application/json", typeof(object))]
    public async Task<HttpResponseData> CheckInProcessAssignmentByQId(
        [HttpTrigger(AuthorizationLevel.Function, "get", Route = "assignments/{qId}/inprocess-validation")]
        HttpRequestData req, long qId)
    {
        try
        {
            logger.LogInformation("Checking InProcess Assignment(s) By Requester's QID {QId}", qId);

            await using var dbContext = await dbContextFactory.CreateDbContextAsync();

            var query = dbContext.Assignment!.AsNoTracking()
                .Join(dbContext.Status!, e => e.Status, s => s.Code,
                    (assignment, status) => new
                    {
                        assignment, status
                    }).AsQueryable();

            var isInProcessRequestExist = await query.AnyAsync(item =>
                item.assignment.QId == qId && item.status.Category! == InProcess);

            logger.LogInformation("Returning InProcess Assignment(s) Validation Result {Result} By Requester's QID {QId}", isInProcessRequestExist, qId);

            return await req.WriteOkResponseAsync(new CheckInProcessAssignmentResponse
            {
                IsInprocessExist = isInProcessRequestExist
            });
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error occurred while checking inprocess assignment by QID {QId}", qId);
            return ex.DefaultExceptionBehaviour(req, logger);
        }
    }

    #endregion

    #region GetAssignmentItemByReqNumber

    /// <summary>
    /// Get assignment item for the given request number
    /// </summary>
    /// <param name="req"></param>
    /// <param name="reqNumber">Assignment Request Number</param>
    /// <returns></returns>
    [Function("GetAssignmentItemByReqNumber")]
    [OpenApiOperation(operationId: "GetAssignmentItemByReqNumber", tags: ["Assignment"],
        Summary = "Get Assignment Item By ReqNumber", Description = "Get assignment item for the given request number")]
    [OpenApiParameter("reqNumber", Description = "Assignment Request Number", In = ParameterLocation.Path,
        Required = true, Type = typeof(string))]
    [OpenApiResponseWithBody(OK, "application/json", typeof(GetAssignmentItemResponse))]
    [OpenApiResponseWithBody(BadRequest, "application/json", typeof(ErrorResponse))]
    [OpenApiResponseWithBody(NoContent, "application/json", typeof(object))]
    [OpenApiResponseWithBody(Unauthorized, "application/json", typeof(object))]
    public async Task<HttpResponseData> GetAssignmentItemByReqNumber(
        [HttpTrigger(AuthorizationLevel.Function, "get", Route = "assignments/{reqNumber}")]
        HttpRequestData req, string reqNumber)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(reqNumber))
            {
                return await req.WriteErrorResponseAsync(BadRequest, "Invalid Req Number");
            }

            logger.LogInformation("Getting Assignment Item By Request Number {ReqNumber}", reqNumber);

            var submitter = req.GetClaims();

            await using var dbContext = await dbContextFactory.CreateDbContextAsync();

            if (dbContext.Assignment == null)
            {
                logger.LogInformation("Assignment is null");
                return await req.WriteNoContentResponseAsync();
            }

            var response = await dbContext.Assignment.AsNoTracking().Include(s => s.StatusNavigation)
                .SingleOrDefaultAsync(item => item.ReqNumber! == reqNumber);

            if (response == null)
            {
                logger.LogWarning("Item not found");
                return await req.WriteNoContentResponseAsync();
            }

            if (submitter.QId != null && response.SubmitterQId != submitter.QId)
            {
                logger.LogWarning(AssignmentConstants.UnauthorizedAccessMessage, response.SubmitterQId);
                return await req.WriteUnauthorizedResponseAsync();
            }

            if (response.StatusNavigation == null)
            {
                logger.LogInformation("StatusNavigation is null");
                return await req.WriteNoContentResponseAsync();
            }

            var getResponse = MapAssignmentToGetAssignmentItemResponse(response);
            getResponse.Status = response.StatusNavigation!.Code;
            getResponse.StatusDescriptionEn = response.StatusNavigation.DescriptionEn;
            getResponse.StatusDescriptionAr = response.StatusNavigation.DescriptionAr;

            logger.LogInformation("Returning Assignment Item By Request Number {ReqNumber}", getResponse.ReqNumber);

            return await req.WriteOkResponseAsync(getResponse);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error occurred while getting assignment item by request number {ReqNumber}", reqNumber);
            return ex.DefaultExceptionBehaviour(req, logger);
        }
    }

    #endregion

    #region Create Assignment

    /// <summary>
    /// Create new assignment request
    /// </summary>
    /// <param name="req">New Assignment Create Model</param>
    /// <returns></returns>
    [Function("CreateAssignment")]
    [OpenApiOperation(operationId: "CreateAssignment", tags: ["Assignment"], Summary = "Create Assignment",
        Description = "Create new assignment request")]
    [OpenApiRequestBody("application/json", typeof(CreateUpdateAssignmentRequest))]
    [OpenApiResponseWithBody(Created, "application/json", typeof(Assignment))]
    [OpenApiResponseWithBody(BadRequest, "application/json", typeof(ErrorResponse))]
    [OpenApiResponseWithBody(InternalServerError, "application/json", typeof(object))]
    public async Task<HttpResponseData> CreateAssignment(
        [HttpTrigger(AuthorizationLevel.Function, "post", Route = "assignments")]
        HttpRequestData req)
    {
        try
        {
            logger.LogInformation("Creating an Assignment");
            var requestOriginSource = req.TryGetHeader(RequestOrigin);

            if (string.IsNullOrWhiteSpace(requestOriginSource))
            {
                return await req.WriteErrorResponseAsync(BadRequest, "Required 'X-RequestOrigin' header value");
            }

            string requestBody = await new StreamReader(req.Body).ReadToEndAsync();
            CreateUpdateAssignmentRequest? request;

            try
            {
                request = DeserializeObject<CreateUpdateAssignmentRequest>(requestBody);

                if (request.SubmitterQId is null)
                {
                    return await req.WriteErrorResponseAsync(BadRequest, AssignmentConstants.InvalidRequestBodyMessage);
                }

                long qId = request.SubmitterQId.Value;
                if (!req.IsAuthorized(qId))
                {
                    logger.LogWarning(AssignmentConstants.UnauthorizedAccessMessage, qId);
                    return await req.WriteUnauthorizedResponseAsync();
                }
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error occurred while deserializing request body {RequestBody}", requestBody);
                return await req.WriteErrorResponseAsync(BadRequest, "Error Occurred While Deserializing Request Body");
            }

            if (IsEmptyObject(request) || request.QId == 0 || request.SubmitterQId.GetValueOrDefault() == 0
                || string.IsNullOrEmpty(request.SubmitterEmail) || string.IsNullOrEmpty(request.SubmitterMobile))
            {
                return await req.WriteErrorResponseAsync(BadRequest, AssignmentConstants.InvalidRequestBodyMessage);
            }
            
            var healthCardValidation = await ValidateHealthCardNumberAsync(
                request.HCNumber, request.QId, "Assignment", req, logger);

            if (!healthCardValidation.IsValid)
            {
                return healthCardValidation.ErrorResponse!;
            }

            await using var dbContext = await dbContextFactory.CreateDbContextAsync();

            // Check for existing in-process assignment requests
            var existingInProcessQuery = dbContext.Assignment!
                .Join(dbContext.Status!, a => a.Status, s => s.Code, (assignment, status) => new { assignment, status })
                .AsQueryable();

            var isInProcessRequestExist = await existingInProcessQuery.AnyAsync(item =>
                item.assignment.QId == request.QId && item.status.Category! == InProcess);

            if (isInProcessRequestExist)
            {
                logger.LogWarning("Duplicate assignment request detected for QId {QId}", request.QId);
                return await req.WriteErrorResponseAsync(BadRequest, "An in-process assignment request already exists for this applicant.");
            }

            Assignment create = MapCreateUpdateAssignmentRequestToAssignment(request);
            create.ReqNumber = RandomPassword();
            create.CreateSource = requestOriginSource;
            create.SubmittedAt = GetCurrentTime();
            create.CreatedAt = GetCurrentTime();
            create.Status = Saved;

            try
            {
                await dbContext.Assignment!.AddAsync(create);
                await dbContext.SaveChangesAsync();
                logger.LogInformation("Created A New Assignment With Request Number {ReqNumber}", create.ReqNumber);
            }
            catch (DbUpdateException e)
            {
                logger.LogError(e, "Error: {ErrorMessage}", e.InnerException?.Message);
                return await req.WriteErrorResponseAsync(InternalServerError,
                    "Error Occurred While Creating Assignment");
            }

            return await req.WriteOkResponseAsync(create, Created);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error occurred while creating assignment");
            return ex.DefaultExceptionBehaviour(req, logger);
        }
    }

    #endregion

    #region Update AssignmentByReqNumber

    /// <summary>
    /// Update assignment for the given request number
    /// </summary>
    /// <param name="req"></param>
    /// <param name="reqNumber">Assignment Request Number </param>
    /// <returns></returns>
    [Function("UpdateAssignmentByReqNumber")]
    [OpenApiOperation(operationId: "UpdateAssignmentByReqNumber", tags: ["Assignment"],
        Summary = "Update Assignment By ReqNumber",
        Description = "Update assignment item for the given request number")]
    [OpenApiRequestBody("application/json", typeof(CreateUpdateAssignmentRequest))]
    [OpenApiParameter("reqNumber", Description = "Assignment Request Number", In = ParameterLocation.Path,
        Required = true, Type = typeof(string))]
    [OpenApiResponseWithBody(BadRequest, "application/json", typeof(object))]
    [OpenApiResponseWithBody(OK, "application/json", typeof(object))]
    [OpenApiResponseWithBody(InternalServerError, "application/json", typeof(object))]
    public async Task<HttpResponseData> UpdateAssignmentByReqNumber(
        [HttpTrigger(AuthorizationLevel.Function, "put", Route = "assignments/{reqNumber}")]
        HttpRequestData req, string reqNumber)
    {
        try
        {
            var requestOriginSource = req.TryGetHeader(RequestOrigin);

            if (string.IsNullOrWhiteSpace(requestOriginSource))
            {
                return await req.WriteErrorResponseAsync(BadRequest, "Required 'X-RequestOrigin' header value");
            }

            string requestBody = await new StreamReader(req.Body).ReadToEndAsync();
            var request = DeserializeObject<CreateUpdateAssignmentRequest>(requestBody);

            if (request.SubmitterQId is null)
            {
                return await req.WriteErrorResponseAsync(BadRequest, AssignmentConstants.InvalidRequestBodyMessage);
            }
            
            var healthCardValidation = await ValidateHealthCardNumberAsync(
                request.HCNumber, request.QId, "Assignment", req, logger);

            if (!healthCardValidation.IsValid)
            {
                return healthCardValidation.ErrorResponse!;
            }

            long qId = request.SubmitterQId.Value;
            if (!req.IsAuthorized(qId))
            {
                logger.LogWarning(AssignmentConstants.UnauthorizedAccessMessage, qId);
                return await req.WriteUnauthorizedResponseAsync();
            }

            if (IsEmptyObject(request))
            {
                return await req.WriteErrorResponseAsync(BadRequest,
                    "Invalid request body. Please try again.");
            }

            if (request.ChangeReason is null)
            {
                return await req.WriteErrorResponseAsync(BadRequest, "Change reason cannot be null!");
            }

            logger.LogInformation("Updating An Assignment with Request Number {ReqNumber}", reqNumber);

            await using var dbContext = await dbContextFactory.CreateDbContextAsync();

            Assignment? response = await dbContext.Assignment!.SingleOrDefaultAsync(item =>
                item.ReqNumber! == reqNumber && item.SubmitterQId == request.SubmitterQId);

            if (response == null)
            {
                logger.LogWarning("Invalid Req Number {ReqNumber}", reqNumber);
                return await req.WriteErrorResponseAsync(BadRequest, string.Format(AssignmentConstants.InvalidReqNumberMessage, reqNumber));
            }

            var mapperResponse = MapCreateUpdateAssignmentRequestToAssignment(request);

            mapperResponse.Id = response.Id;
            mapperResponse.ReqNumber = response.ReqNumber;
            mapperResponse.Status = response.Status;
            mapperResponse.StatusInternal = response.StatusInternal;
            mapperResponse.SN = response.SN;
            mapperResponse.SubmittedAt = GetCurrentTime();
            mapperResponse.CreatedAt = response.CreatedAt;
            mapperResponse.UpdatedAt = GetCurrentTime();
            mapperResponse.CreateSource = response.CreateSource;
            mapperResponse.UpdateSource = requestOriginSource;

            dbContext.Entry(response).CurrentValues.SetValues(mapperResponse);
            await dbContext.SaveChangesAsync();
            logger.LogInformation("Updated A New Assignment With Request Number {ReqNumber}", response.ReqNumber);

            return await req.WriteOkResponseAsync(mapperResponse);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error occurred while updating assignment by request number {ReqNumber}", reqNumber);
            return ex.DefaultExceptionBehaviour(req, logger);
        }
    }

    #endregion

    #region Delete Assignment

    /// <summary>
    /// Delete assignment for the given request number
    /// </summary>
    /// <param name="req"></param>
    /// <param name="reqNumber">Assignment Request Number </param>
    /// <returns></returns>
    [Function("DeleteAssignmentByReqNumber")]
    [OpenApiOperation(operationId: "DeleteAssignmentByReqNumber", tags: ["Assignment"],
        Summary = "Delete Assignment By ReqNumber",
        Description = "Delete assignment item for the given request number")]
    [OpenApiParameter("reqNumber", Description = "Assignment Request Number", In = ParameterLocation.Path,
        Required = true, Type = typeof(string))]
    [OpenApiResponseWithBody(BadRequest, "application/json", typeof(ErrorResponse))]
    [OpenApiResponseWithBody(NoContent, "application/json", typeof(object))]
    [OpenApiResponseWithBody(InternalServerError, "application/json", typeof(object))]
    [OpenApiIgnore]
    public async Task<HttpResponseData> DeleteAssignmentByReqNumber(
        [HttpTrigger(AuthorizationLevel.Function, "delete", Route = "assignments/{reqNumber}")]
        HttpRequestData req, string reqNumber)
    {
        try
        {
            var submitter = req.GetClaims();
            await using var dbContext = await dbContextFactory.CreateDbContextAsync();
            var response = await dbContext.Assignment!.AsTracking()
                .SingleOrDefaultAsync(item => item.ReqNumber! == reqNumber);

            if (response == null)
            {
                logger.LogWarning("Invalid Req Number {ReqNumber}", reqNumber);
                return await req.WriteErrorResponseAsync(BadRequest, string.Format(AssignmentConstants.InvalidReqNumberMessage, reqNumber));
            }

            if (submitter.QId != null && response.SubmitterQId != submitter.QId)
            {
                logger.LogWarning(AssignmentConstants.UnauthorizedAccessMessage, response.SubmitterQId);
                return await req.WriteUnauthorizedResponseAsync();
            }

            logger.LogInformation("Deleting An Assignment");

            try
            {
                dbContext.Assignment!.Remove(response);
                await dbContext.SaveChangesAsync();
                logger.LogInformation("Deleted Assignment By Request Number {ReqNumber}", response.ReqNumber);
            }
            catch (DbUpdateException ex)
            {
                logger.LogError(ex, "Error: {ErrorMessage}", ex.InnerException!.Message);
                return ex.DefaultExceptionBehaviour(req, logger);
            }

            logger.LogInformation("Deleted Assignment By Request Number {ReqNumber}", response.ReqNumber);

            return await req.WriteOkResponseAsync("Successfully Deleted");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error occurred while deleting assignment by request number {ReqNumber}", reqNumber);
            return ex.DefaultExceptionBehaviour(req, logger);
        }
    }

    #endregion
}