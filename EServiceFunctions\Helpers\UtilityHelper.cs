using System.Security;
using EServiceFunctions.Models.Shared;
using Microsoft.Extensions.Primitives;
using static System.MidpointRounding;
using static System.String;
using static System.Text.Encoding;
using static Newtonsoft.Json.Formatting;

namespace EServiceFunctions.Helpers;

[ExcludeFromCodeCoverage]
public static class UtilityHelper
{
    public static string? TryGetHeader(this HttpRequestData req, string? headerName)
    {
        try
        {
            return req.Headers.TryGetValues(headerName, out var headerValue)
                ? headerValue.FirstOrDefault()
                : throw new ArgumentNullException(headerName);
        }
        catch (ArgumentNullException ex)
        {
            throw new ArgumentNullException($"Error while getting header {nameof(headerName)}", ex);
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException($"Error while getting header {nameof(headerName)}", ex);
        }
    }

    public static List<string> TryGetHeaders(this HttpRequestData req, string headerName)
    {
        return req.Headers.TryGetValues(headerName, out var headerValue)
            ? SplitHeaders(headerValue)
            : [];
    }

    private static List<string> SplitHeaders(IEnumerable<string> headerValue)
    {
        try
        {
            return headerValue.SelectMany(x => x.Split(',').Select(y => y.Trim())).ToList();
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException("Error while splitting headers", ex);
        }
    }

    public static Submitter GetClaims(this HttpRequestData req)
    {
        try
        {
            var claimQId = req.TryGetHeader(JwtClaimsQId);

            return new Submitter
            {
                QId = claimQId is not null ? long.Parse(claimQId) : 0
            };
        }
        catch (FormatException ex)
        {
            throw new FormatException($"Error while parsing claim QId: {ex.Message}", ex);
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException($"Error while getting claims {ex.Message} ", ex);
        }
    }

    public static bool IsAuthorized(this HttpRequestData req, long submitter)
    {
        try
        {
            string authValidationRequired = req.TryGetHeader(IsAuthValReq);
            if (authValidationRequired == null) return false;
            if (!bool.TryParse(authValidationRequired, out var isAuthRequired) || !isAuthRequired) return true;
            return IsClaimMatchingSubmitter(GetClaims(req), submitter);
        }
        catch
        {
            return false;
        }
    }

    private static bool IsClaimMatchingSubmitter(Submitter claims, long submitterQId)
    {
        return submitterQId == claims.QId;
    }

    public static bool IsBooleanHeader(this HttpRequestData req, string headerName)
    {
        return bool.TryParse(req.TryGetHeader(headerName), out var result) && result;
    }

    public static bool GetBooleanQueryParameter(this HttpRequestData request, string parameterKey)
    {
        var queryStringValue = request.GetCleanedQueryString(parameterKey);
        bool.TryParse(queryStringValue, out var result);
        return result;
    }

    public static int GetIntQueryParameter(this HttpRequestData req, string parameterName, int defaultValue = 0)
    {
        var isParsed = int.TryParse(req.GetCleanedQueryString(parameterName), out var result);
        result = result < 0 ? result * -1 : result;
        if (parameterName == "take")
        {
            return isParsed ? result : 100;
        }

        if (isParsed && parameterName == "skip" && result > 0)
        {
            return result;
        }

        return defaultValue;
    }

    private static string GetTrimmedQueryString(this HttpRequestData request, string? key)
    {
        if (!request.Url.Query.Contains(key))
            return EmptyString;
        StringValues values = request.Query[key];
        return values.Count > 0 ? values[0].Trim() : EmptyString;
    }

    public static string GetCleanedQueryString(this HttpRequestData request, string? key)
    {
        return request.GetTrimmedQueryString(key).RemoveWhitespaces();
    }

    public static string RemoveWhitespaces(this string? value)
    {
        return value == null ? throw new ArgumentNullException(nameof(value)) : value.Replace(" ", Empty);
    }

    public static bool IsNotNullOrWhiteSpace(this string value) => !IsNullOrWhiteSpace(value);

    public static string LogRequestedUrl(this HttpRequestData req)
    {
        return $"{req.Url.Scheme}://{req.Url.Host}{req.Url.AbsolutePath}";
    }

    private static string IndentedJsonSerializer(object obj)
    {
        return SerializeObject(obj, Indented);
    }

    public static string ToBase64(this Stream stream)
    {
        byte[] bytes;
        using (var memoryStream = new MemoryStream())
        {
            stream.CopyTo(memoryStream);
            bytes = memoryStream.ToArray();
        }

        string base64 = ToBase64String(bytes);
        return base64;
    }

    public static bool IsValidFileExtensionByFileName(string fileName)
    {
        string extension = Path.GetExtension(fileName);
        string[] extensions = [".PNG", ".JPG", ".PDF", ".JPEG"];
        return Array.Exists(extensions, x => string.Equals(x, extension, OrdinalIgnoreCase));
    }

    public static bool IsValidFileExtension(string base64String)
    {
        var validFileExtensions = new HashSet<string> { "IVBOR", "/9J/4", "JVBER" };
        return validFileExtensions.Contains(base64String[..5].ToUpperInvariant());
    }

    private static bool IsValidRange(long fileSizeBits)
    {
        return Math.Round((double)fileSizeBits / OneMb, 3, AwayFromZero) is > 0 and <= 5;
    }

    public static bool IsValidFileSize(long fileSizeBits)
    {
        return IsValidRange(fileSizeBits);
    }

    public static bool IsValidFileSize(string base64String)
    {
        if (IsNullOrWhiteSpace(base64String)) return false;
        long fileSize = ToInt64(3 * (base64String.Length / 4f) - base64String[^2..].Count(c => c == '='));
        return IsValidRange(fileSize);
    }

    public static HttpResponseData DefaultExceptionBehaviour(this Exception exception, HttpRequestData req,
        ILogger<object> log, string message = EmptyString)
    {
        log.PrintLog(exception);
        message = message.IsNotNullOrWhiteSpace() ? message : exception.Message;
        return DetermineActionResultBasedOnExceptionType(req, exception, message);
    }
    
    private const string MissingHealthCardMsg = "Health Card Number is required.";
    private const string InvalidHealthCardFormatMsg = "Health Card Number format is invalid.";

    public static async Task<HealthCardValidationResult> ValidateHealthCardNumberAsync<T>(
        string? healthCardNumber,
        long qId,
        string requestType,
        HttpRequestData req,
        ILogger<T> logger)
    {
        using var scope = logger.BeginScope(new Dictionary<string, object>
        {
            ["QId"] = qId,
            ["RequestType"] = requestType,
            ["Source"] = req.TryGetHeader(RequestOrigin) ?? "Unknown",
            ["Url"] = req.LogRequestedUrl(),
            ["Timestamp"] = DateTime.UtcNow
        });

        // 1. Check for missing health card number
        if (IsNullOrWhiteSpace(healthCardNumber))
        {
            logger.LogWarning("Validation Failed: Health Card Number is missing");
            var errorResponse = await req.WriteErrorResponseAsync(BadRequest, MissingHealthCardMsg);
            return new HealthCardValidationResult(false, errorResponse);
        }

        // 2. Check for invalid format
        if (!IsValidHealthCardFormat(healthCardNumber))
        {
            // Log the specific invalid value for diagnostics, the scope adds the rest.
            logger.LogWarning("Validation Failed: Invalid format for Health Card Number {HealthCardNumber}", healthCardNumber);
            var errorResponse = await req.WriteErrorResponseAsync(BadRequest, InvalidHealthCardFormatMsg);
            return new HealthCardValidationResult(false, errorResponse);
        }
        
        logger.LogInformation("Health Card Number validation successful");
        return new HealthCardValidationResult(true, null);
    }
    
    private static bool IsValidHealthCardFormat(string healthCardNumber) =>
        !IsNullOrWhiteSpace(healthCardNumber) &&
        healthCardNumber.Trim() is { Length: >= 8 and <= 12 } cleaned &&
        cleaned.All(char.IsLetterOrDigit);

    private static void PrintLog(this ILogger log, Exception? exception)
    {
        if (exception is not null)
        {
            for (Exception? ex = exception; ex != null; ex = ex.InnerException)
            {
                log.LogError(ex, "An error occurred: {Message}. StackTrace: {StackTrace}", ex.Message, ex.StackTrace);
            }
        }
        else
        {
            log.LogWarning("Provided exception is null. No exception to log");
        }
    }

    private static HttpResponseData DetermineActionResultBasedOnExceptionType(HttpRequestData req, Exception exception,
        string message)
    {
        var exceptionMappings = new Dictionary<Type, Func<HttpResponseData>>
        {
            { typeof(TypeInitializationException), () => CreateErrorResponse(req, InternalServerError, message) },
            { typeof(UnauthorizedAccessException), () => CreateErrorResponse(req, Unauthorized, message) },
            { typeof(DbUpdateException), () => CreateErrorResponse(req, BadRequest, message) },
            { typeof(ArgumentNullException), () => CreateErrorResponse(req, BadRequest, message) },
            { typeof(ArgumentException), () => CreateErrorResponse(req, BadRequest, message) },
            { typeof(PathTooLongException), () => CreateErrorResponse(req, BadRequest, message) },
            { typeof(IOException), () => CreateErrorResponse(req, BadRequest, message) },
            { typeof(NotSupportedException), () => CreateErrorResponse(req, BadRequest, message) },
            { typeof(InvalidOperationException), () => CreateErrorResponse(req, BadRequest, message) },
            { typeof(NullReferenceException), () => CreateErrorResponse(req, BadRequest, message) },
            { typeof(FileNotFoundException), () => CreateErrorResponse(req, NotFound, message) },
            { typeof(DirectoryNotFoundException), () => CreateErrorResponse(req, NotFound, message) },
            { typeof(SystemException), () => CreateErrorResponse(req, InternalServerError, message) },
            { typeof(SecurityException), () => CreateErrorResponse(req, Forbidden, message) }
        };

        return exceptionMappings.TryGetValue(exception.GetType(), out var resultFactory)
            ? resultFactory()
            : CreateErrorResponse(req, BadRequest, message);
    }

    private static HttpResponseData CreateErrorResponse(HttpRequestData req, HttpStatusCode status, string message)
    {
        var response = req.CreateResponse(status);
        var errorResponse = new ErrorResponse(status, message);
        var json = IndentedJsonSerializer(errorResponse);
        var jsonData = UTF8.GetBytes(json);
        response.Body.Write(jsonData, 0, jsonData.Length);
        return response;
    }
}