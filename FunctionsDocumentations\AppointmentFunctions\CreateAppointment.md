# CreateAppointment

## Overview
Creates a new appointment request in the system.

## Endpoint
- **Route**: `appointments`
- **Method**: POST
- **Authorization**: Function level

## Request Body
```json
{
  "QId": "string",
  "HCNumber": "string",
  "RequestType": "string",
  "Clinic": "string",
  "PrefDate": "date",
  "PrefContactTime": "string",
  "SubmitterEmail": "string",
  "SubmitterMobile": "string"
}
```

### Required Fields
- **HcNumber**: Health Card Number is mandatory for all appointment requests. Must be a valid alphanumeric string between 5-20 characters.

## Headers
- `EligibleClinicCodes`: List of eligible clinic codes
- Authorization header with submitter's QID

## Responses
- **201 Created**: Returns created appointment details
  ```json
  {
    "ReqNumber": "string",
    "Status": "string",
    "Message": "string"
  }
  ```
- **400 Bad Request**: Invalid request data or validation failures
  - Missing Health Card Number:
    ```json
    {
      "code": 400,
      "message": "Health Card No is missing for QID {QID} and request name Appointment"
    }
    ```
  - Invalid Health Card Format:
    ```json
    {
      "code": 400,
      "message": "Health Card No format is invalid for QID {QID} and request name Appointment"
    }
    ```
- **401 Unauthorized**: Authentication failed
- **403 Forbidden**: Access denied
- **500 Internal Server Error**: Server error

## Business Logic
1. Validates request data and headers
2. **Validates Health Card Number** (NEW):
   - Checks if Health Card Number is present
   - Validates format (5-20 alphanumeric characters)
   - Returns 400 Bad Request if validation fails
   - Logs validation failures with timestamp, QID, request type, and source
3. Generates unique request number
4. Sets initial appointment status
5. Creates appointment record
6. Returns created appointment details

## Dependencies
- AppointmentContext
- Status entity
